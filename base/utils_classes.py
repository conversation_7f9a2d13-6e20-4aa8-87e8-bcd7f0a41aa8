'''
NOTE variables starting with _
     won't be used outside this module
'''

from django.conf import settings

from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum

from natsort import natsorted

from base.country_codes import COUNTRY_CODES_DICT

# from base.models import WindowsServer

from base.windows_server_audit_events import WINDOWS_SERVER_AUDIT_EVENTS

from base.snort_classifications import (
    CLASSIFICATIONS__CRITICALS,
    CLASSIFICATIONS__LOWS,
    CLASSIFICATIONS__VERY_LOWS,
    CLASSIFICATIONS__WARNINGS,
    CLASSIFICATIONS_DICT,
)


HOUR_KEYS: tuple[str, ...] = (
    '00:00 - 00:59',
    '01:00 - 01:59',
    '02:00 - 02:59',
    '03:00 - 03:59',
    '04:00 - 04:59',
    '05:00 - 05:59',
    '06:00 - 06:59',
    '07:00 - 07:59',
    '08:00 - 08:59',
    '09:00 - 09:59',
    '10:00 - 10:59',
    '11:00 - 11:59',
    '12:00 - 12:59',
    '13:00 - 13:59',
    '14:00 - 14:59',
    '15:00 - 15:59',
    '16:00 - 16:59',
    '17:00 - 17:59',
    '18:00 - 18:59',
    '19:00 - 19:59',
    '20:00 - 20:59',
    '21:00 - 21:59',
    '22:00 - 22:59',
    '23:00 - 23:59',
)

## https://community.cisco.com/t5/other-network-architecture-subjects/what-is-logging-facility-local7/td-p/373768
EVENT_TYPES: tuple[str, ...] = (
    '(local7/alert)',
    '(local7/crit)',
    '(local7/debug)',
    '(local7/emerg)',
    '(local7/err)',
    '(local7/info)',
    '(local7/notice)',
    '(local7/warning)',
)
EVENT_TYPES__CRITICALS: tuple[str, ...] = (
    _ for _ in EVENT_TYPES
    if any([
        '/alert' in _,
        '/crit' in _,
        '/emerg' in _,
        '/err' in _,
    ])
)
EVENT_TYPES__WARNINGS: tuple[str, ...] = (
    _ for _ in EVENT_TYPES
    if any([
        '/warning' in _,
    ])
)

## https://community.cisco.com/t5/other-network-architecture-subjects/what-is-logging-facility-local7/td-p/373768
_EVENT_TYPES__DAEMON: tuple[str, ...] = (
    '(daemon/alert)',
    '(daemon/crit)',
    '(daemon/debug)',
    '(daemon/emerg)',
    '(daemon/err)',
    '(daemon/info)',
    '(daemon/notice)',
    '(daemon/warning)',
)
_EVENT_TYPES__DAEMON__CRITICALS: tuple[str, ...] = (
    _ for _ in _EVENT_TYPES__DAEMON
    if any([
        '/alert' in _,
        '/crit' in _,
        '/emerg' in _,
        '/err' in _,
    ])
)
_EVENT_TYPES__DAEMON__WARNINGS: tuple[str, ...] = (
    _ for _ in _EVENT_TYPES__DAEMON
    if any([
        '/warning' in _,
    ])
)

## -------------------------------
## *Config

class MYSQLConfig(Enum):
    '''
    Enum class containing MySQL database configuration variables and constants.

    This class defines data types, database naming conventions, connection credentials,
    and other MySQL-related configuration settings used throughout the application.

    Attributes:
        ID_DATA_TYPE (str): MySQL data type for id field.
        DATE_DATA_TYPE (str): MySQL data type for date fields.
        TIME_DATA_TYPE (str): MySQL data type for time fields.
        DEFAULT_DATA_TYPE (str): Default MySQL data type for text fields.
        INDEXED_COLUMN_DATA_TYPE (str): MySQL data type for indexed columns.
        COUNT_DATA_TYPE (str): MySQL data type for count fields.
        BUILTIN_DATABASES (list[str]): List of MySQL built-in database names to exclude.
        INFILE_CHUNKSIZE (int): Chunk size for loading data into MySQL.
        ENCLOSED_BY (str): Field enclosure character for data import.
        INDEX_PREFIX_LENGTH (int): Prefix length for text field indexing.
        INDEX_TYPE (str): Type of index to use (BTREE for better range query support).
        TERMINATED_BY (str): Field terminator for data import.
        DB_NAME_SEPARATOR (str): Separator used in database names.
        TABLE_NAME_SEPARATOR (str): Separator used in table names.
        POOL_CHUNKSIZE (int): Chunk size for connection pooling.
        MASTER_CREDS (dict[str, str]): Master database connection credentials.
        R_USER_CREDS (dict[str, str]): Read-only user database connection credentials.
        NON_DATED_DATABASES (list[str]): List of databases that don't use date-based naming.

    Examples:
        >>> MYSQLConfig.DEFAULT_DATA_TYPE.value
        'MEDIUMTEXT'

        >>> MYSQLConfig.get_infile_statement()
        'LOAD DATA INFILE'  # In production mode
    '''

    ID_DATA_TYPE: str             = 'INT PRIMARY KEY AUTO_INCREMENT'
    DATE_DATA_TYPE: str           = 'VARCHAR(10)'
    TIME_DATA_TYPE: str           = 'VARCHAR(13)'
    DEFAULT_DATA_TYPE: str        = 'MEDIUMTEXT'
    INDEXED_COLUMN_DATA_TYPE: str = 'VARCHAR(5000)'
    COUNT_DATA_TYPE: str          = 'INT'

    BUILTIN_DATABASES: tuple[str, ...] = (
        'information_schema',
        'mysql',
        'performance_schema',
        'sys',
    )

    INFILE_CHUNKSIZE: int        = 5_000_000
    ENCLOSED_BY: str             = ''  ## '"' caused errors
    INDEX_PREFIX_LENGTH: int     = 100  ## changed from 50 for better text field indexing
    INDEX_TYPE: str              = 'BTREE'  ## changed from HASH for better range query support
    TERMINATED_BY: str           = '-*@*-'
    DB_NAME_SEPARATOR: str       = '__'  ## __DATABASE_YMD_PATTERN__
    TABLE_NAME_SEPARATOR: str    = '__'

    POOL_CHUNKSIZE: int = 100_000

    ## for MySQLdb
    MASTER_CREDS: dict[str, str] = {
        'host':     settings.MYSQL_HOST,
        'user':     settings.MYSQL_MASTER,
        'password': settings.MYSQL_MASTER_PASSWD,
    }
    R_USER_CREDS: dict[str, str] = {
        'host':     settings.MYSQL_HOST,
        'user':     settings.MYSQL_R_USER,
        'password': settings.MYSQL_R_USER_PASSWD,
    }

    NON_DATED_DATABASES: tuple[str, ...] = (
        'geolocation',  ## JUMP_13
        'malicious',  ## JUMP_14
    )

    @classmethod
    def get_infile_statement(cls):
        '''
        Returns the appropriate LOAD DATA statement based on the environment.

        In debug mode, uses LOCAL INFILE which allows loading from client machine.
        In production, uses standard INFILE which loads from server file system.

        Returns:
            str: The appropriate LOAD DATA statement for the current environment.

        Examples:
            >>> MYSQLConfig.get_infile_statement()  # In debug mode
            'LOAD DATA LOCAL INFILE'

            >>> MYSQLConfig.get_infile_statement()  # In production mode
            'LOAD DATA INFILE'
        '''
        if settings.DEBUG:
            return 'LOAD DATA LOCAL INFILE'
        return 'LOAD DATA INFILE'

class GeoLocationConfig(Enum):
    '''
    Enum class containing geolocation-related configuration variables and constants.

    This class defines database schemas, field mappings, and utility methods for
    handling geolocation data for both domains and IP addresses.

    Attributes:
        TITLE (str): Display title for the geolocation module.
        SLUG (str): Identifier slug used in database naming.
        DB_HEADERS_WITH_INDEXES__DOMAIN (dict): Mapping of domain field names to column indices.
        DB_HEADERS__DOMAIN (list): List of column names for domain geolocation data.
        DB_COLUMNS__DOMAIN (str): SQL column definitions for domain geolocation table.
        DB_KEYS__DOMAIN (str): Column names for domain geolocation data insertion.
        DB_MARKS__DOMAIN (str): Placeholder string for domain data SQL insertion.
        DB_HEADERS_WITH_INDEXES__IP (dict): Mapping of IP field names to column indices.
        DB_HEADERS__IP (list): List of column names for IP geolocation data.
        DB_COLUMNS__IP (str): SQL column definitions for IP geolocation table.
        DB_KEYS__IP (str): Column names for IP geolocation data insertion.
        DB_MARKS__IP (str): Placeholder string for IP data SQL insertion.
        COUNTRY_CODES_DICT (dict): Dictionary mapping country codes to country names.
    '''

    TITLE: str = 'GeoLocation'
    SLUG: str = 'geolocation'  ## JUMP_13

    DB_HEADERS_WITH_INDEXES__DOMAIN: dict[str, int] = {
        'ID':           0,
        'Domain':       1,
        'Country':      2,
        'Country Code': 3,
        'Region':       4,
        'City':         5,
        'Latitude':     6,
        'Longitude':    7,
        'Timezone':     8,
        'ISP':          9,
        'Organization': 10,
        'IP':           11,
    }
    DB_HEADERS__DOMAIN: list[str] = [
        'ID',
        'Domain',
        'Country',
        'Country Code',
        'Region',
        'City',
        'Latitude',
        'Longitude',
        'Timezone',
        'ISP',
        'Organization',
        'IP',
    ]
    DB_COLUMNS__DOMAIN: str = f'''
        ID             {MYSQLConfig.ID_DATA_TYPE.value},
        Domain         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Country        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Country Code` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Region         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        City           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Latitude       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Longitude      {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Timezone       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        ISP            {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Organization   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        IP             {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS__DOMAIN: str = '''
        Domain,
        Country,
        `Country Code`,
        Region,
        City,
        Latitude,
        Longitude,
        Timezone,
        ISP,
        Organization,
        IP
    '''
    DB_MARKS__DOMAIN: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    DB_HEADERS_WITH_INDEXES__IP: dict[str, int] = {
        'ID':                 0,
        'IP':                 1,
        'Type':               2,
        'Continent':          3,
        'Continent Code':     4,
        'Country':            5,
        'Country Code':       6,
        'City':               7,
        'Latitude':           8,
        'Longitude':          9,
        'Is EU':              10,
        'ASN':                11,
        'Organization':       12,
        'ISP':                13,
        'Domain':             14,
        'Timezone':           15,
        'Flag Emoji':         16,
        'Flag Emoji Unicode': 17,
        'Flag IMG':           18,
    }
    DB_HEADERS__IP: list[str] = [
        'ID',
        'IP',  ## ***********
        'Type',  ## IPv4
        'Continent',  ## Europe
        'Continent Code',  ## EU
        'Country',  ## Romania
        'Country Code',  ## RO
        'City',  ## Suceava
        'Latitude',  ## 47.6634521
        'Longitude',  ## 26.2732302
        'Is EU',  ## true
        'ASN',  ## 8708
        'Organization',  ## Rcs Rds Residential
        'ISP',  ## DIGI ROMANIA S.A.
        'Domain',  ## digi.ro
        'Timezone',  ## Europe/Bucharest
        'Flag Emoji',  ## 🇷🇴
        'Flag Emoji Unicode',  ## U+1F1F7 U+1F1F4
        'Flag IMG',  ## https://cdn.ipwhois.io/flags/ro.svg
    ]
    DB_COLUMNS__IP: str = f'''
        ID                   {MYSQLConfig.ID_DATA_TYPE.value},
        IP                   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Type                 {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Continent            {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Continent Code`     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Country              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Country Code`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        City                 {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Latitude             {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Longitude            {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Is EU`              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        ASN                  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Organization         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        ISP                  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Domain               {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Timezone             {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Flag Emoji`         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Flag Emoji Unicode` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Flag IMG`           {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS__IP: str = '''
        IP,
        Type,
        Continent,
        `Continent Code`,
        Country,
        `Country Code`,
        City,
        Latitude,
        Longitude,
        `Is EU`,
        ASN,
        Organization,
        ISP,
        Domain,
        Timezone,
        `Flag Emoji`,
        `Flag Emoji Unicode`,
        `Flag IMG`
    '''
    DB_MARKS__IP: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    COUNTRY_CODES_DICT: dict[str, str] = COUNTRY_CODES_DICT

    @classmethod
    def get_table_name(cls, geo_mode: str) -> str:
        '''
        Args:
            geo_mode (str): The mode of geolocation data to select.
                            It can be either 'domain' or 'ip'.

        Returns:
            str: A SQL table name string for the specified geolocation mode.

        Examples:
            >>> GeoLocationConfig.get_table_name(geo_mode='domain')
            'geolocationtable__domain'

            >>> GeoLocationConfig.get_table_name(geo_mode='ip')
            'geolocationtable__ip'
        '''

        return f'{cls.SLUG.value}table{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{geo_mode}'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls, geo_mode: str) -> str:
        return f'SELECT * FROM {cls.get_table_name(geo_mode=geo_mode)}'

class MaliciousConfig(Enum):
    TITLE: str = 'Malicious'
    SLUG: str = 'malicious'  ## JUMP_14

    ## shared by parent and children
    DB_HEADERS_WITH_INDEXES__DOMAIN: dict[str, int] = {
        'ID':      0,
        'Domain':  1,
        'Sources': 2,
    }
    DB_HEADERS__DOMAIN: list[str] = [
        'ID',
        'Domain',
        'Sources',
    ]
    DB_COLUMNS__DOMAIN: str = f'''
        ID      {MYSQLConfig.ID_DATA_TYPE.value},
        Domain  {MYSQLConfig.INDEXED_COLUMN_DATA_TYPE.value},
        Sources {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS__DOMAIN: str = '''
        Domain,
        Sources
    '''
    DB_MARKS__DOMAIN: str = '%s,%s'

    ## used by parent and children
    DB_HEADERS_WITH_INDEXES__IP: dict[str, int] = {
        'ID':      0,
        'IP':      1,
        'Sources': 2,
    }
    DB_HEADERS__IP: list[str] = [
        'ID',
        'IP',
        'Sources',
    ]
    DB_COLUMNS__IP: str = f'''
        ID      {MYSQLConfig.ID_DATA_TYPE.value},
        IP      {MYSQLConfig.INDEXED_COLUMN_DATA_TYPE.value},
        Sources {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS__IP: str = '''
        IP,
        Sources
    '''
    DB_MARKS__IP: str = '%s,%s'

    ## used by parent
    TABLENAMES_AND_KEYS_FOR_INDEX__PARENT: list[tuple[str, str]] = [
        ('malicioustable__domain__parent', 'Domain'),
        ('malicioustable__ip__parent',     'IP'),
    ]

    ## used by children
    TABLENAMES_AND_KEYS_FOR_INDEX: list[tuple[str, str]] = [
        ('malicioustable__domain', 'Domain'),
        ('malicioustable__ip',     'IP'),
    ]

    @classmethod
    def get_table_name(cls, mal_mode: str, is_parent: bool) -> str:
        '''
        Generates a SQL table name for malicious data based on mode and parent status.

        Args:
            mal_mode (str): The mode of malicious data to select.
                            It can be either 'domain' or 'ip'.
            is_parent (bool): True if is parent, False otherwise.

        Returns:
            str: A SQL table name string for the specified malicious mode and parent status.

        Examples:
            >>> MaliciousConfig.get_table_name(mal_mode='domain', is_parent=True)
            'malicioustable__domain__parent'

            >>> MaliciousConfig.get_table_name(mal_mode='domain', is_parent=False)
            'malicioustable__domain'

            >>> MaliciousConfig.get_table_name(mal_mode='ip', is_parent=True)
            'malicioustable__ip__parent'

            >>> MaliciousConfig.get_table_name(mal_mode='ip', is_parent=False)
            'malicioustable__ip'
        '''

        if is_parent:
            suffix = f'{MYSQLConfig.TABLE_NAME_SEPARATOR.value}parent'
        else:
            suffix = ''

        return f'{cls.SLUG.value}table{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{mal_mode}{suffix}'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed logs related to malicious data.

        Returns:
            str: The directory path for malicious data parsed logs.
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls, mal_mode: str, is_parent: bool) -> str:
        '''
        Generates a SQL SELECT statement for retrieving malicious data.

        Args:
            mal_mode (str): The mode of malicious data to select ('domain' or 'ip').
            is_parent (bool): True if selecting from parent table, False otherwise.

        Returns:
            str: A SQL SELECT statement for the specified malicious mode and parent status.
        '''
        return f'SELECT * FROM {cls.get_table_name(mal_mode=mal_mode, is_parent=is_parent)}'

class DaemonConfig(Enum):
    '''
    Enum class containing constants and utility methods for the Daemon module.

    This class defines database structure, filtering criteria, and helper methods
    for working with daemon log data.


    Attributes:
        TITLE (str): Display title for the module ('Daemon').
        SLUG (str): URL and identifier slug ('daemon').
        FILTERBY (str): String used to filter daemon logs.
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported daemon event types.
        EVENT_TYPES__CRITICALS (list[str]): Critical daemon event types.
        EVENT_TYPES__WARNINGS (list[str]): Warning daemon event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'Daemon'
    SLUG: str = 'daemon'

    FILTERBY: str = '(daemon/'
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: list[str] = _EVENT_TYPES__DAEMON
    EVENT_TYPES__CRITICALS: list[str] = _EVENT_TYPES__DAEMON__CRITICALS
    EVENT_TYPES__WARNINGS: list[str] = _EVENT_TYPES__DAEMON__WARNINGS

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Level':      3,
        'Alert Type': 4,
        'Message':    5,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Level',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        Level        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Level,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for daemon data.

        Returns:
            str: The SQL table name for daemon data.

        Examples:
            >>> DaemonConfig.get_table_name()
            'daemontable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed daemon logs.

        Returns:
            str: The directory path for daemon parsed logs.

        Examples:
            >>> DaemonConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/daemon'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all daemon data.

        Returns:
            str: A SQL SELECT statement for daemon data.

        Examples:
            >>> DaemonConfig.get_select_statement()
            'SELECT * FROM daemontable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class DHCPConfig(Enum):
    '''
    Enum class containing constants and utility methods for the DHCP module.

    This class defines database structure, filtering criteria, and helper methods
    for working with DHCP log data.

    Attributes:
        TITLE (str): Display title for the module ('DHCP').
        SLUG (str): URL and identifier slug ('dhcp').
        FILTERBY (str): String used to filter DHCP logs.
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported DHCP event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'DHCP'
    SLUG: str = 'dhcp'

    FILTERBY: str = '[dhcp]'
    FILTERBY_IS_IN_ALERT_TYPE: bool = True
    EVENT_TYPES: tuple[str, ...] = (
        '(syslog/info)',
        '(user/notice)',
    )

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':                    0,
        'Date':                  1,
        'Time':                  2,
        'Event ID':              3,
        'Description':           4,
        'Source IP':             5,
        'Host Name':             6,
        'MAC Address':           7,
        'User Name':             8,
        'TransactionID':         9,
        'QResult':               10,
        'Probationtime':         11,
        'CorrelationID':         12,
        'Dhcid':                 13,
        'VendorClass(Hex)':      14,
        'VendorClass(ASCII)':    15,
        'UserClass(Hex)':        16,
        'UserClass(ASCII)':      17,
        'RelayAgentInformation': 18,
        'DnsRegError':           19,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Event ID',
        'Description',
        'Source IP',
        'Host Name',
        'MAC Address',
        'User Name',
        'TransactionID',
        'QResult',
        'Probationtime',
        'CorrelationID',
        'Dhcid',
        'VendorClass(Hex)',
        'VendorClass(ASCII)',
        'UserClass(Hex)',
        'UserClass(ASCII)',
        'RelayAgentInformation',
        'DnsRegError',
    ]
    DB_COLUMNS: str = f'''
        ID                    {MYSQLConfig.ID_DATA_TYPE.value},
        Date                  {MYSQLConfig.DATE_DATA_TYPE.value},
        Time                  {MYSQLConfig.TIME_DATA_TYPE.value},
        `Event ID`            {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Description           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source IP`           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Host Name`           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `MAC Address`         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `User Name`           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        TransactionID         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        QResult               {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Probationtime         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        CorrelationID         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Dhcid                 {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `VendorClass(Hex)`    {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `VendorClass(ASCII)`  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `UserClass(Hex)`      {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `UserClass(ASCII)`    {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        RelayAgentInformation {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        DnsRegError           {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `Event ID`,
        Description,
        `Source IP`,
        `Host Name`,
        `MAC Address`,
        `User Name`,
        TransactionID,
        QResult,
        Probationtime,
        CorrelationID,
        Dhcid,
        `VendorClass(Hex)`,
        `VendorClass(ASCII)`,
        `UserClass(Hex)`,
        `UserClass(ASCII)`,
        RelayAgentInformation,
        DnsRegError
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for DHCP data.

        Returns:
            str: The SQL table name for DHCP data.

        Examples:
            >>> DHCPConfig.get_table_name()
            'dhcptable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed DHCP logs.

        Returns:
            str: The directory path for DHCP parsed logs.

        Examples:
            >>> DHCPConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/dhcp'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all DHCP data.

        Returns:
            str: A SQL SELECT statement for DHCP data.

        Examples:
            >>> DHCPConfig.get_select_statement()
            'SELECT * FROM dhcptable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class DNSConfig(Enum):
    '''
    Enum class containing constants and utility methods for the DNS module.

    This class defines database structure, filtering criteria, and helper methods
    for working with DNS log data.

    Attributes:
        TITLE (str): Display title for the module ('DNS').
        SLUG (str): URL and identifier slug ('dns').
        FILTERBY (str): String used to filter DNS logs.
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported DNS event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'DNS'
    SLUG: str = 'dns'

    FILTERBY: str = '[dns]'
    FILTERBY_IS_IN_ALERT_TYPE: bool = True
    EVENT_TYPES: tuple[str, ...] = (
        '(syslog/info)',
        '(user/notice)',
    )

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':                         0,
        'Date':                       1,
        'Time':                       2,
        'Thread ID':                  3,
        'Context':                    4,
        'Internal Packet Identifier': 5,
        'UDP/TCP Indicator':          6,
        'Send/Receive Indicator':     7,
        'Source IP':                  8,
        'Xid (hex)':                  9,
        'Query/Response':             10,
        'Opcode':                     11,
        'Flags (hex)':                12,
        'Flags (char codes)':         13,
        'ResponseCode':               14,
        'Question Type':              15,
        'Question Name':              16,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Thread ID',
        'Context',
        'Internal Packet Identifier',
        'UDP/TCP Indicator',
        'Send/Receive Indicator',
        'Source IP',
        'Xid (hex)',
        'Query/Response',
        'Opcode',
        'Flags (hex)',
        'Flags (char codes)',
        'ResponseCode',
        'Question Type',
        'Question Name',
    ]
    DB_COLUMNS: str = f'''
        ID                           {MYSQLConfig.ID_DATA_TYPE.value},
        Date                         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time                         {MYSQLConfig.TIME_DATA_TYPE.value},
        `Thread ID`                  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Context                      {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Internal Packet Identifier` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `UDP/TCP Indicator`          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Send/Receive Indicator`     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source IP`                  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Xid (hex)`                  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Query/Response`             {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Opcode                       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Flags (hex)`                {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Flags (char codes)`         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        ResponseCode                 {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Question Type`              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Question Name`              {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `Thread ID`,
        Context,
        `Internal Packet Identifier`,
        `UDP/TCP Indicator`,
        `Send/Receive Indicator`,
        `Source IP`,
        `Xid (hex)`,
        `Query/Response`,
        Opcode,
        `Flags (hex)`,
        `Flags (char codes)`,
        ResponseCode,
        `Question Type`,
        `Question Name`
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for DNS data.

        Returns:
            str: The SQL table name for DNS data.

        Examples:
            >>> DNSConfig.get_table_name()
            'dnstable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed DNS logs.

        Returns:
            str: The directory path for DNS parsed logs.

        Examples:
            >>> DNSConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/dns'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all DNS data.

        Returns:
            str: A SQL SELECT statement for DNS data.

        Examples:
            >>> DNSConfig.get_select_statement()
            'SELECT * FROM dnstable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class FilterLogConfig(Enum):
    '''
    Enum class containing constants and utility methods for the FilterLog module.

    This class defines database structure, filtering criteria, and helper methods
    for working with filter log data.

    Attributes:
        TITLE (str): Display title for the module ('FilterLog').
        SLUG (str): URL and identifier slug ('filterlog').
        FILTERBY (str): String used to filter logs ('[filterlog]').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported FilterLog event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'FilterLog'
    SLUG: str = 'filterlog'

    FILTERBY: str = '[filterlog]'
    FILTERBY_IS_IN_ALERT_TYPE: bool = True
    EVENT_TYPES: tuple[str, ...] = tuple()

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':               0,
        'Date':             1,
        'Time':             2,
        'Protocol Name':    3,
        'Source IP':        4,
        'Destination IP':   5,
        'Source Port':      6,
        'Destination Port': 7,
        'Tracking ID':      8,
        'Real Interface':   9,
        'Reason':           10,
        'Action':           11,
        'Direction':        12,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Protocol Name',
        'Source IP',
        'Destination IP',
        'Source Port',
        'Destination Port',
        'Tracking ID',
        'Real Interface',
        'Reason',
        'Action',
        'Direction',
    ]
    DB_COLUMNS: str = f'''
        ID                  {MYSQLConfig.ID_DATA_TYPE.value},
        Date                {MYSQLConfig.DATE_DATA_TYPE.value},
        Time                {MYSQLConfig.TIME_DATA_TYPE.value},
        `Protocol Name`     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source IP`         {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination IP`    {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source Port`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination Port`  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Tracking ID`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Real Interface`    {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Reason              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Action              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Direction           {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `Protocol Name`,
        `Source IP`,
        `Destination IP`,
        `Source Port`,
        `Destination Port`,
        `Tracking ID`,
        `Real Interface`,
        Reason,
        Action,
        Direction
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for FilterLog data.

        Returns:
            str: The SQL table name for FilterLog data.

        Examples:
            >>> FilterLogConfig.get_table_name()
            'filterlogtable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed FilterLog logs.

        Returns:
            str: The directory path for FilterLog parsed logs.

        Examples:
            >>> FilterLogConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/filterlog'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all FilterLog data.

        Returns:
            str: A SQL SELECT statement for FilterLog data.

        Examples:
            >>> FilterLogConfig.get_select_statement()
            'SELECT * FROM filterlogtable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class RouterConfig(Enum):
    '''
    Enum class containing constants and utility methods for the Router module.

    This class defines database structure, filtering criteria, and helper methods
    for working with router log data.

    Attributes:
        TITLE (str): Display title for the module ('Router').
        SLUG (str): URL and identifier slug ('router').
        FILTERBY (str): String used to filter logs ('').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported Router event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'Router'
    SLUG: str = 'router'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: list[str] = EVENT_TYPES

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Level':      3,
        'Alert Type': 4,
        'Message':    5,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Level',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        Level        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Level,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for Router data.

        Returns:
            str: The SQL table name for Router data.

        Examples:
            >>> RouterConfig.get_table_name()
            'routertable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed Router logs.

        Returns:
            str: The directory path for Router parsed logs.

        Examples:
            >>> RouterConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/router'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all Router data.

        Returns:
            str: A SQL SELECT statement for Router data.

        Examples:
            >>> RouterConfig.get_select_statement()
            'SELECT * FROM routertable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class RouterBoardConfig(Enum):
    '''
    Enum class containing constants and utility methods for the RouterBoard module.

    This class defines database structure, filtering criteria, and helper methods
    for working with RouterBoard log data.

    Attributes:
        TITLE (str): Display title for the module ('RouterBoard').
        SLUG (str): URL and identifier slug ('routerboard').
        FILTERBY (str): String used to filter logs ('').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported RouterBoard event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'RouterBoard'
    SLUG: str = 'routerboard'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: list[str] = EVENT_TYPES

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Level':      3,
        'Alert Type': 4,
        'Message':    5,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Level',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        Level        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Level,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for RouterBoard data.

        Returns:
            str: The SQL table name for RouterBoard data.

        Examples:
            >>> RouterBoardConfig.get_table_name()
            'routerboardtable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed RouterBoard logs.

        Returns:
            str: The directory path for RouterBoard parsed logs.

        Examples:
            >>> RouterBoardConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/routerboard'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all RouterBoard data.

        Returns:
            str: A SQL SELECT statement for RouterBoard data.

        Examples:
            >>> RouterBoardConfig.get_select_statement()
            'SELECT * FROM routerboardtable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class SnortConfig(Enum):
    '''
    Enum class containing constants and utility methods for the Snort module.

    This class defines database structure, filtering criteria, and helper methods
    for working with Snort intrusion detection log data.

    Attributes:
        TITLE (str): Display title for the module ('Snort').
        SLUG (str): URL and identifier slug ('snort').
        FILTERBY (str): String used to filter logs ('[snort]').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (True).
        EVENT_TYPES (list[str]): All supported Snort event types ('(auth/alert)').
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
        TABLENAMES_AND_KEYS_FOR_INDEX (list[tuple[str, str]]): Tables and columns to be indexed.
        CLASSIFICATIONS_DICT (dict): Dictionary of Snort classification mappings.
        CLASSIFICATIONS__CRITICALS (list[str]): List of critical severity classifications.
        CLASSIFICATIONS__WARNINGS (list[str]): List of warning severity classifications.
        CLASSIFICATIONS__LOWS (list[str]): List of low severity classifications.
        CLASSIFICATIONS__VERY_LOWS (list[str]): List of very low severity classifications.
    '''
    TITLE: str = 'Snort'
    SLUG: str = 'snort'

    FILTERBY: str = '[snort]'
    FILTERBY_IS_IN_ALERT_TYPE: bool = True
    EVENT_TYPES: tuple[str, ...] = ('(auth/alert)',)

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':               0,
        'Date':             1,
        'Time':             2,
        'GID:SID':          3,
        'Description':      4,
        'Classification':   5,
        'Priority':         6,
        'Protocol':         7,
        'Source IP':        8,
        'Source Port':      9,
        'Destination IP':   10,
        'Destination Port': 11,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'GID:SID',
        'Description',
        'Classification',
        'Priority',
        'Protocol',
        'Source IP',
        'Source Port',
        'Destination IP',
        'Destination Port',
    ]
    DB_COLUMNS: str = f'''
        ID                 {MYSQLConfig.ID_DATA_TYPE.value},
        Date               {MYSQLConfig.DATE_DATA_TYPE.value},
        Time               {MYSQLConfig.TIME_DATA_TYPE.value},
        `GID:SID`          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Description        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Classification     {MYSQLConfig.INDEXED_COLUMN_DATA_TYPE.value},
        Priority           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Protocol           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source IP`        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source Port`      {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination IP`   {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination Port` {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `GID:SID`,
        Description,
        Classification,
        Priority,
        Protocol,
        `Source IP`,
        `Source Port`,
        `Destination IP`,
        `Destination Port`
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    TABLENAMES_AND_KEYS_FOR_INDEX: list[tuple[str, str]] = [
        ('snorttable', 'Classification'),
    ]

    CLASSIFICATIONS_DICT: dict = CLASSIFICATIONS_DICT
    ##
    CLASSIFICATIONS__CRITICALS: list[str] = natsorted(CLASSIFICATIONS__CRITICALS)
    CLASSIFICATIONS__WARNINGS: list[str] = natsorted(CLASSIFICATIONS__WARNINGS)
    CLASSIFICATIONS__LOWS: list[str] = natsorted(CLASSIFICATIONS__LOWS)
    CLASSIFICATIONS__VERY_LOWS: list[str] = natsorted(CLASSIFICATIONS__VERY_LOWS)

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for Snort data.

        Returns:
            str: The SQL table name for Snort data.

        Examples:
            >>> SnortConfig.get_table_name()
            'snorttable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed Snort logs.

        Returns:
            str: The directory path for Snort parsed logs.

        Examples:
            >>> SnortConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/snort'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all Snort data.

        Returns:
            str: A SQL SELECT statement for Snort data.

        Examples:
            >>> SnortConfig.get_select_statement()
            'SELECT * FROM snorttable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class SquidConfig(Enum):
    '''
    Enum class containing constants and utility methods for the Squid module.

    This class defines database structure, filtering criteria, and helper methods
    for working with Squid proxy server log data.

    Attributes:
        TITLE (str): Display title for the module ('Squid').
        SLUG (str): URL and identifier slug ('squid').
        FILTERBY (str): String used to filter logs ('[(squid-1)]').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (True).
        EVENT_TYPES (list[str]): Supported Squid event types (empty list).
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'Squid'
    SLUG: str = 'squid'

    FILTERBY: str = '[(squid-1)]'
    FILTERBY_IS_IN_ALERT_TYPE: bool = True
    EVENT_TYPES: tuple[str, ...] = tuple()

    ## https://community.splunk.com/t5/Getting-Data-In/How-to-to-extract-fields-from-Squid-logs-to-Splunk-from-PFsense/m-p/152579
    ## https://kifarunix.com/create-squid-logs-extractors-on-graylog-server/

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':              0,
        'Date':            1,
        'Time':            2,
        'Duration':        3,
        'Source IP':       4,
        'Request Status':  5,
        'Status Code':     6,
        'Transfer':        7,
        'HTTP Method':     8,
        'URL':             9,
        'Client Identity': 10,
        'Peer Code':       11,
        'Destination IP':  12,
        'Content Type':    13,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Duration',
        'Source IP',
        'Request Status',
        'Status Code',
        'Transfer',
        'HTTP Method',
        'URL',
        'Client Identity',
        'Peer Code',
        'Destination IP',
        'Content Type',
    ]
    DB_COLUMNS: str = f'''
        ID                {MYSQLConfig.ID_DATA_TYPE.value},
        Date              {MYSQLConfig.DATE_DATA_TYPE.value},
        Time              {MYSQLConfig.TIME_DATA_TYPE.value},
        Duration          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source IP`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Request Status`  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Status Code`     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Transfer          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `HTTP Method`     {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        URL               {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Client Identity` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Peer Code`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination IP`  {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Content Type`    {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Duration,
        `Source IP`,
        `Request Status`,
        `Status Code`,
        Transfer,
        `HTTP Method`,
        URL,
        `Client Identity`,
        `Peer Code`,
        `Destination IP`,
        `Content Type`
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for Squid data.

        Returns:
            str: The SQL table name for Squid data.

        Examples:
            >>> SquidConfig.get_table_name()
            'squidtable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed Squid logs.

        Returns:
            str: The directory path for Squid parsed logs.

        Examples:
            >>> SquidConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/squid'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all Squid data.

        Returns:
            str: A SQL SELECT statement for Squid data.

        Examples:
            >>> SquidConfig.get_select_statement()
            'SELECT * FROM squidtable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class SwitchConfig(Enum):
    '''
    Enum class containing constants and utility methods for the Switch module.

    This class defines database structure, filtering criteria, and helper methods
    for working with switch log data.

    Attributes:
        TITLE (str): Display title for the module ('Switch').
        SLUG (str): URL and identifier slug ('switch').
        FILTERBY (str): String used to filter logs (empty string).
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (False).
        EVENT_TYPES (list[str]): Supported switch event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'Switch'
    SLUG: str = 'switch'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: list[str] = EVENT_TYPES

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Level':      3,
        'Alert Type': 4,
        'Message':    5,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Level',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        Level        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Level,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for Switch data.

        Returns:
            str: The SQL table name for Switch data.

        Examples:
            >>> SwitchConfig.get_table_name()
            'switchtable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed Switch logs.

        Returns:
            str: The directory path for Switch parsed logs.

        Examples:
            >>> SwitchConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/switch'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all Switch data.

        Returns:
            str: A SQL SELECT statement for Switch data.

        Examples:
            >>> SwitchConfig.get_select_statement()
            'SELECT * FROM switchtable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class UserAuditConfig(Enum):
    '''
    Enum class containing constants and utility methods for the UserAudit module.

    This class defines database structure, filtering criteria, and helper methods
    for working with user audit log data.

    Attributes:
        TITLE (str): Display title for the module ('User Audit').
        SLUG (str): URL and identifier slug ('useraudit').
        FILTERBY (str): String used to filter logs (empty string).
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (False).
        EVENT_TYPES (list[str]): Supported user audit event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'User Audit'
    SLUG: str = 'useraudit'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: tuple[str, ...] = (
        '(daemon/err)',
        '(auth/emerg)',
    )

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Alert Type': 3,
        'Message':    4,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for UserAudit data.

        Returns:
            str: The SQL table name for UserAudit data.

        Examples:
            >>> UserAuditConfig.get_table_name()
            'useraudittable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed UserAudit logs.

        Returns:
            str: The directory path for UserAudit parsed logs.

        Examples:
            >>> UserAuditConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/useraudit'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all UserAudit data.

        Returns:
            str: A SQL SELECT statement for UserAudit data.

        Examples:
            >>> UserAuditConfig.get_select_statement()
            'SELECT * FROM useraudittable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class UserNoticeConfig(Enum):
    '''
    Enum class containing constants and utility methods for the UserNotice module.

    This class defines database structure, filtering criteria, and helper methods
    for working with user notice log data, particularly OpenVPN logs.

    Attributes:
        TITLE (str): Display title for the module ('User Notice').
        SLUG (str): URL and identifier slug ('usernotice').
        FILTERBY (str): String used to filter logs ('[openvpn]').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (True).
        EVENT_TYPES (list[str]): Supported user notice event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'User Notice'
    SLUG: str = 'usernotice'

    FILTERBY: str = '[openvpn]'
    FILTERBY_IS_IN_ALERT_TYPE: bool = True
    EVENT_TYPES: tuple[str, ...] = ('(user/notice)',)

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':             0,
        'Date':           1,
        'Time':           2,
        'Server':         3,
        'User':           4,
        'Destination IP': 5,
        'Port':           6,
        'Status':         7,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Server',
        'User',
        'Destination IP',
        'Port',
        'Status',
    ]
    DB_COLUMNS: str = f'''
        ID               {MYSQLConfig.ID_DATA_TYPE.value},
        Date             {MYSQLConfig.DATE_DATA_TYPE.value},
        Time             {MYSQLConfig.TIME_DATA_TYPE.value},
        Server           {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        User             {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination IP` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Port             {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Status           {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Server,
        User,
        `Destination IP`,
        Port,
        Status
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for UserNotice data.

        Returns:
            str: The SQL table name for UserNotice data.

        Examples:
            >>> UserNoticeConfig.get_table_name()
            'usernoticetable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed UserNotice logs.

        Returns:
            str: The directory path for UserNotice parsed logs.

        Examples:
            >>> UserNoticeConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/usernotice'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all UserNotice data.

        Returns:
            str: A SQL SELECT statement for UserNotice data.

        Examples:
            >>> UserNoticeConfig.get_select_statement()
            'SELECT * FROM usernoticetable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class UserWarningConfig(Enum):
    '''
    Enum class containing constants and utility methods for the UserWarning module.

    This class defines database structure, filtering criteria, and helper methods
    for working with user warning log data.

    Attributes:
        TITLE (str): Display title for the module ('User Warning').
        SLUG (str): URL and identifier slug ('userwarning').
        FILTERBY (str): String used to filter logs ('(user/warning)').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (False).
        EVENT_TYPES (list[str]): Supported user warning event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
        PACKET_LOSS_DB_HEADERS (list[str]): List of packet loss database column names.
    '''
    TITLE: str = 'User Warning'
    SLUG: str = 'userwarning'

    FILTERBY: str = '(user/warning)'
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: tuple[str, ...] = ('(user/warning)',)

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Alert Type': 3,
        'Message':    4,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s'

    PACKET_LOSS_DB_HEADERS: list[str] = [
        'ID',
        'Gateway',
        'Count',
        'Minimum',
        'Maximum',
        'Average',
    ]

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for UserWarning data.

        Returns:
            str: The SQL table name for UserWarning data.

        Examples:
            >>> UserWarningConfig.get_table_name()
            'userwarningtable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed UserWarning logs.

        Returns:
            str: The directory path for UserWarning parsed logs.

        Examples:
            >>> UserWarningConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/userwarning'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all UserWarning data.

        Returns:
            str: A SQL SELECT statement for UserWarning data.

        Examples:
            >>> UserWarningConfig.get_select_statement()
            'SELECT * FROM userwarningtable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class VMwareConfig(Enum):
    '''
    Enum class containing constants and utility methods for the VMware module.

    This class defines database structure, filtering criteria, and helper methods
    for working with VMware log data.

    Attributes:
        TITLE (str): Display title for the module ('VMware').
        SLUG (str): URL and identifier slug ('vmware').
        FILTERBY (str): String used to filter logs ('').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (False).
        EVENT_TYPES (list[str]): Supported VMware event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'VMware'
    SLUG: str = 'vmware'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: tuple[str, ...] = tuple()

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':         0,
        'Date':       1,
        'Time':       2,
        'Level':      3,
        'Alert Type': 4,
        'Message':    5,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Level',
        'Alert Type',
        'Message',
    ]
    DB_COLUMNS: str = f'''
        ID           {MYSQLConfig.ID_DATA_TYPE.value},
        Date         {MYSQLConfig.DATE_DATA_TYPE.value},
        Time         {MYSQLConfig.TIME_DATA_TYPE.value},
        Level        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Alert Type` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message      {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Level,
        `Alert Type`,
        Message
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for VMware data.

        Returns:
            str: The SQL table name for VMware data.

        Examples:
            >>> VMwareConfig.get_table_name()
            'vmwaretable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed VMware logs.

        Returns:
            str: The directory path for VMware parsed logs.

        Examples:
            >>> VMwareConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/vmware'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all VMware data.

        Returns:
            str: A SQL SELECT statement for VMware data.

        Examples:
            >>> VMwareConfig.get_select_statement()
            'SELECT * FROM vmwaretable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class VPNServerConfig(Enum):
    '''
    Enum class containing constants and utility methods for the VPN Server module.

    This class defines database structure, filtering criteria, and helper methods
    for working with VPN Server log data.

    Attributes:
        TITLE (str): Display title for the module ('VPN Server').
        SLUG (str): URL and identifier slug ('vpnserver').
        FILTERBY (str): String used to filter VPN Server logs.
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type.
        EVENT_TYPES (list[str]): All supported VPN Server event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
    '''
    TITLE: str = 'VPN Server'
    SLUG: str = 'vpnserver'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: tuple[str, ...] = tuple()

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':              0,
        'Date':            1,
        'Time':            2,
        'Domain':          3,
        'Username':        4,
        'Port':            5,
        'Active For':      6,
        'Sent':            7,
        'Received':        8,
        'Source IP':       9,
        'Destination IPs': 10,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Domain',
        'Username',
        'Port',
        'Active For',
        'Sent',
        'Received',
        'Source IP',
        'Destination IPs',
    ]
    DB_COLUMNS: str = f'''
        ID                {MYSQLConfig.ID_DATA_TYPE.value},
        Date              {MYSQLConfig.DATE_DATA_TYPE.value},
        Time              {MYSQLConfig.TIME_DATA_TYPE.value},
        Domain            {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Username          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Port              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Active For`      {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Sent              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Received          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source IP`       {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Destination IPs` {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        Domain,
        Username,
        Port,
        `Active For`,
        Sent,
        Received,
        `Source IP`,
        `Destination IPs`
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    @classmethod
    def get_table_name(cls) -> str:
        '''
        Returns the table name for VPNServer data.

        Returns:
            str: The SQL table name for VPNServer data.

        Examples:
            >>> VPNServerConfig.get_table_name()
            'vpnservertable'
        '''
        return f'{cls.SLUG.value}table'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed VPNServer logs.

        Returns:
            str: The directory path for VPNServer parsed logs.

        Examples:
            >>> VPNServerConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/vpnserver'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls) -> str:
        '''
        Returns a SQL SELECT statement for retrieving all VPNServer data.

        Returns:
            str: A SQL SELECT statement for VPNServer data.

        Examples:
            >>> VPNServerConfig.get_select_statement()
            'SELECT * FROM vpnservertable'
        '''
        return f'SELECT * FROM {cls.get_table_name()}'

class WindowsServerConfig(Enum):
    '''
    Enum class containing constants and utility methods for the Windows Server module.

    This class defines database structure, filtering criteria, and helper methods
    for working with Windows Server log data.

    Attributes:
        TITLE (str): Display title for the module ('Windows Server').
        SLUG (str): URL and identifier slug ('windowsserver').
        FILTERBY (str): String used to filter logs ('').
        FILTERBY_IS_IN_ALERT_TYPE (bool): Whether filter string appears in alert type (False).
        EVENT_TYPES (list[str]): Supported Windows Server event types.
        DB_HEADERS_WITH_INDEXES (dict[str, int]): Column names with their positions.
        DB_HEADERS (list[str]): List of database column names.
        DB_COLUMNS (str): SQL column definitions for table creation.
        DB_KEYS (str): Column names for SQL insert statements.
        DB_MARKS (str): Placeholder string for SQL prepared statements.
        EVENT_IDS_AND_POTENTIAL_CRITICALITIES (dict[str, str]): Mapping of event IDs to criticality levels.
        EVENT_IDS__HIGH (list[str]): List of event IDs with high criticality.
        EVENT_IDS__MEDIUM (list[str]): List of event IDs with medium criticality.
        HIGH_POTENTIAL_CRITICALITIES (list[str]): List of high criticality level strings.
        MEDIUM_POTENTIAL_CRITICALITIES (list[str]): List of medium criticality level strings.
        EVENT_IDS__ACCOUNT_LOGON (list[str]): List of event IDs in Account Logon category.
        EVENT_IDS__ACCOUNT_MANAGEMENT (list[str]): List of event IDs in Account Management category.
        EVENT_IDS__DETAILED_TRACKING (list[str]): List of event IDs in Detailed Tracking category.
        EVENT_IDS__DS_ACCESS (list[str]): List of event IDs in DS Access category.
        EVENT_IDS__LOGONLOGOFF (list[str]): List of event IDs in Logon/Logoff category.
        EVENT_IDS__OBJECT_ACCESS (list[str]): List of event IDs in Object Access category.
        EVENT_IDS__POLICY_CHANGE (list[str]): List of event IDs in Policy Change category.
        EVENT_IDS__PRIVILEGE_USE (list[str]): List of event IDs in Privilege Use category.
        EVENT_IDS__SYSTEM (list[str]): List of event IDs in System category.
        EVENT_IDS__MISCELLANEOUS (list[str]): List of event IDs in Miscellaneous category.
    '''
    TITLE: str = 'Windows Server'
    SLUG: str = 'windowsserver'

    FILTERBY: str = ''
    FILTERBY_IS_IN_ALERT_TYPE: bool = False
    EVENT_TYPES: tuple[str, ...] = tuple()

    DB_HEADERS_WITH_INDEXES: dict[str, int] = {
        'ID':                    0,
        'Date':                  1,
        'Time':                  2,
        'Alert Type':            3,
        'Message':               4,
        'Event ID':              5,
        'Category':              6,
        'Potential Criticality': 7,
        'Account Name':          8,
        'Account Domain':        9,
        'Source Workstation':    10,
    }
    DB_HEADERS: list[str] = [
        'ID',
        'Date',
        'Time',
        'Alert Type',
        'Message',
        'Event ID',
        'Category',
        'Potential Criticality',
        'Account Name',
        'Account Domain',
        'Source Workstation',
    ]
    DB_COLUMNS: str = f'''
        ID                      {MYSQLConfig.ID_DATA_TYPE.value},
        Date                    {MYSQLConfig.DATE_DATA_TYPE.value},
        Time                    {MYSQLConfig.TIME_DATA_TYPE.value},
        `Alert Type`            {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Message                 {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Event ID`              {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        Category                {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Potential Criticality` {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Account Name`          {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Account Domain`        {MYSQLConfig.DEFAULT_DATA_TYPE.value},
        `Source Workstation`    {MYSQLConfig.DEFAULT_DATA_TYPE.value}
    '''
    DB_KEYS: str = '''
        Date,
        Time,
        `Alert Type`,
        Message,
        `Event ID`,
        Category,
        `Potential Criticality`,
        `Account Name`,
        `Account Domain`,
        `Source Workstation`
    '''
    DB_MARKS: str = '%s,%s,%s,%s,%s,%s,%s,%s,%s,%s'

    EVENT_IDS_AND_POTENTIAL_CRITICALITIES: dict[str, str] = {
        event_id: info.get('Potential Criticality')
        for event_id, info in WINDOWS_SERVER_AUDIT_EVENTS.items()
    }
    ## {
    ##     '4745': 'Low',
    ##     '4746': 'Medium',
    ##     ...
    ## }

    ## used to highlight warning/critical rows of html tables
    ##
    EVENT_IDS__HIGH: tuple[str, ...] = (
        event_id
        for event_id, info in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Potential Criticality') in [
            'High',
        ]
    )
    ##
    EVENT_IDS__MEDIUM: tuple[str, ...] = (
        event_id
        for event_id, info in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Potential Criticality') in [
            'Medium',
            'Medium to High',
        ]
    )
    ##
    HIGH_POTENTIAL_CRITICALITIES: tuple[str, ...] = (
        'High',
    )
    ##
    MEDIUM_POTENTIAL_CRITICALITIES: tuple[str, ...] = (
        'Medium',
        'Medium to High',
    )

    EVENT_IDS__ACCOUNT_LOGON: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Account Logon'
    )
    ## ['4608', '4609', ...]

    EVENT_IDS__ACCOUNT_MANAGEMENT: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Account Management'
    )

    EVENT_IDS__DETAILED_TRACKING: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Detailed Tracking'
    )

    EVENT_IDS__DS_ACCESS: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'DS Access'
    )

    EVENT_IDS__LOGONLOGOFF: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Logon/Logoff'
    )

    EVENT_IDS__OBJECT_ACCESS: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Object Access'
    )

    EVENT_IDS__POLICY_CHANGE: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Policy Change'
    )

    EVENT_IDS__PRIVILEGE_USE: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Privilege Use'
    )

    EVENT_IDS__SYSTEM: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'System'
    )

    EVENT_IDS__MISCELLANEOUS: tuple[str, ...] = (
        event_id for event_id, info
        in WINDOWS_SERVER_AUDIT_EVENTS.items()
        if info.get('Category') == 'Miscellaneous'
    )

    ## NOTE keep the default None for category because
    ##      in live-parse.py, category is not passed to the method
    @classmethod
    def get_table_name(cls, category: str = None) -> str:
        '''
        Args:
            category (str, optional): The category of the Windows Server logs.
                                    If None, returns the default table.
                                    Possible values are:
                                    - 'accountlogon'
                                    - 'accountmanagement'
                                    - 'detailedtracking'
                                    - 'dsaccess'
                                    - 'logonlogoff'
                                    - 'objectaccess'
                                    - 'policychange'
                                    - 'privilegeuse'
                                    - 'system'
                                    - 'miscellaneous'

        Returns:
            str: A SQL table name string for the specified category.

        Examples:
            >>> WindowsServerConfig.get_table_name(category=None)
            'windowsservertable'

            >>> WindowsServerConfig.get_table_name(category='accountlogon')
            'windowsservertable__accountlogon'
        '''

        if category:
            suffix = f'{MYSQLConfig.TABLE_NAME_SEPARATOR.value}{category}'
        else:
            suffix = ''

        return f'{cls.SLUG.value}table{suffix}'

    @classmethod
    def get_logs_parsed_dir(cls) -> str:
        '''
        Returns the directory path for parsed Windows Server logs.

        Returns:
            str: The directory path for Windows Server parsed logs.

        Examples:
            >>> WindowsServerConfig.get_logs_parsed_dir()
            '/path/to/logs_parsed/windowsserver'
        '''
        return f'{settings.LOGS_PARSED_DIR}/{cls.SLUG.value}'

    @classmethod
    def get_select_statement(cls, category: str = None) -> str:
        '''
        Returns a SQL SELECT statement for retrieving Windows Server data.

        Args:
            category (str, optional): The category of Windows Server logs to query.
                                    If None, queries the default table.

        Returns:
            str: A SQL SELECT statement for Windows Server data.

        Examples:
            >>> WindowsServerConfig.get_select_statement()
            'SELECT * FROM windowsservertable'

            >>> WindowsServerConfig.get_select_statement(category='accountlogon')
            'SELECT * FROM windowsservertable__accountlogon'
        '''
        return f'SELECT * FROM {cls.get_table_name(category=category)}'

    def get_category_from_event_id(event_id: str) -> str:
        '''
        Retrieve the category of a Windows Server audit event based on its event ID.

        Args:
            event_id (str): The event ID of the Windows Server audit event.

        Returns:
            str: The category of the audit event if found, otherwise an empty string.

        Examples:
            >>> get_category_from_event_id('4624')
            'Logon/Logoff'

            >>> get_category_from_event_id('9999')
            ''
        '''

        return WINDOWS_SERVER_AUDIT_EVENTS.get(str(event_id), {}).get('Category', '')

## -------------------------------
## *Parser

@dataclass
class Parser:
    '''
    Base parser class for handling log data.

    This class provides common functionality for parsing and managing log data,
    including tracking rows, instance management, and identification.
    '''

    def __str__(self):
        _string = f'<<{self.slug}'

        try:
            if self.object_name:
                _string += f' - {self.object_name}'

            if self.__class__.__name__ == 'WindowsServerParser':
                _string += f' (category: {self.category})'
        except AttributeError:
            pass

        try:
            if self.ymd:
                _string += f' - {self.ymd}'
        except AttributeError:
            pass

        _string += f' -- id: {id(self)}>>'

        return _string

    @property
    def no_of_rows(self):
        return len(self.rows)

    def truncate_rows(self):
        self.rows = None

    '''
    @classmethod
    def get_instance(cls, slug, ymd, object_name=None, category=None):
        _filtered: list = []

        for _i in cls.get_instances():
            ## passed_3 and passed_4 are set to True by default
            ## because object_name and category
            ## may be not passed in by some parsers
            passed_1, \
            passed_2, \
            passed_3, \
            passed_4 = False, False, True, True

            ## NOTE do NOT if -> elif

            if _i.slug == slug:
                passed_1 = True

            if _i.ymd == ymd:
                passed_2 = True

            if object_name:
                passed_3 = False
                if _i.object_name == object_name:
                    passed_3 = True

            ## for Windows Server
            if cls.__name__ == 'WindowsServerParser':
                passed_4 = False
                if _i.category == category:
                    passed_4 = True

            if passed_1 and \
               passed_2 and \
               passed_3 and \
               passed_4:
                _filtered.append(_i)

        _filtered = set(_filtered)

        ## we intend to only get one instance
        ## so if more than one is found
        ## we'll return None
        ## as if none was found
        if not len(_filtered) == 1:
            return None

        ## return the only item of set
        ## (https://stackoverflow.com/a/1619539/)
        return next(iter(_filtered))
    '''

@dataclass
class GeoLocationParser(Parser):
    '''
    Parser class for handling geolocation data.

    This class provides functionality for parsing and managing geolocation data
    for both domains and IP addresses, including tracking various attributes
    like countries, cities, organizations, etc.

    Attributes:
        slug (str): Identifier for the parser type.
        rows (list): Collection of parsed entries.
        fetched_all (list): Collection of all fetched data.
        countries_and_counts (list): Country data and occurrence counts.
        cities_and_counts (list): City data and occurrence counts.
        timezones_and_counts (list): Timezone data and occurrence counts.
        isps_and_counts (list): ISP data and occurrence counts.
        organizations_and_counts (list): Organization data and occurrence counts.
        ips_and_counts (list): IP address data and occurrence counts (for domains).
        types_and_counts (list): IP type data and occurrence counts (for IPs).
        continents_and_counts (list): Continent data and occurrence counts (for IPs).
        domains_and_counts (list): Domain data and occurrence counts (for IPs).
    '''
    slug: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    ## used for domain and ip
    fetched_all: list = field(default_factory=list)

    ## used for domain
    countries_and_counts: list = field(default_factory=list)
    cities_and_counts: list = field(default_factory=list)
    timezones_and_counts: list = field(default_factory=list)
    isps_and_counts: list = field(default_factory=list)
    organizations_and_counts: list = field(default_factory=list)
    ips_and_counts: list = field(default_factory=list)

    ## used for ip
    types_and_counts: list = field(default_factory=list)
    continents_and_counts: list = field(default_factory=list)
    domains_and_counts: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        ## used for domain and ip
        self.fetched_all = None

        ## used for domain
        self.countries_and_counts     = None
        self.cities_and_counts        = None
        self.timezones_and_counts     = None
        self.isps_and_counts          = None
        self.organizations_and_counts = None
        self.ips_and_counts           = None

        ## used for ip
        self.types_and_counts         = None
        self.continents_and_counts    = None
        self.countries_and_counts     = None
        self.cities_and_counts        = None
        self.organizations_and_counts = None
        self.isps_and_counts          = None
        self.domains_and_counts       = None
        self.timezones_and_counts     = None

@dataclass
class MaliciousParser(Parser):
    '''
    Parser class for handling malicious data.

    This class provides functionality for parsing and managing malicious data
    for both domains and IP addresses, including tracking sources.

    Attributes:
        slug (str): Identifier for the parser type.
        rows (list): Collection of parsed entries.
        fetched_all (list): Collection of all fetched data.
        ips_and_sources__parent (defaultdict): Mapping of IPs to their sources in parent.
        domains_and_sources__parent (defaultdict): Mapping of domains to their sources in parent.
        domains_and_sources (dict): Mapping of domains to their sources in children.
        ips_and_sources (dict): Mapping of IPs to their sources in children.
    '''
    slug: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    ## used by parent
    fetched_all: list = field(default_factory=list)
    ips_and_sources__parent: defaultdict = field(default_factory=lambda: defaultdict(list))
    domains_and_sources__parent: defaultdict = field(default_factory=lambda: defaultdict(list))

    ## used by children
    domains_and_sources: dict = field(default_factory=dict)
    ips_and_sources: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        ## used by parent
        self.fetched_all                 = None
        self.ips_and_sources__parent     = None
        self.domains_and_sources__parent = None

        ## used by children
        self.domains_and_sources = None
        self.ips_and_sources     = None

@dataclass
class DaemonParser(Parser):
    '''
    Parser class for handling daemon log data.

    This class provides functionality for parsing and managing daemon logs,
    including tracking error and warning counts.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
        daemon_errors_count (int): Count of error messages.
        daemon_warnings_count (int): Count of warning messages.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    ## for levelcounttable
    daemon_errors_count: int = 0
    daemon_warnings_count: int = 0

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        ## for levelcounttable
        self.daemon_errors_count   = None
        self.daemon_warnings_count = None

@dataclass
class DHCPParser(Parser):
    '''
    Parser class for handling DHCP log data.

    This class provides functionality for parsing and managing DHCP logs,
    including tracking various attributes like times, descriptions, IPs,
    hostnames, and MAC addresses.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        descriptions_and_counts (dict): Mapping of descriptions to occurrence counts.
        source_ips_and_counts (dict): Mapping of source IPs to occurrence counts.
        host_names_and_counts (dict): Mapping of hostnames to occurrence counts.
        mac_addresses_and_counts (dict): Mapping of MAC addresses to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
        ips_macs (dict): Mapping of IP addresses to MAC addresses.
    '''
    slug: str
    ymd: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    descriptions_and_counts: dict = field(default_factory=dict)
    source_ips_and_counts: dict = field(default_factory=dict)
    host_names_and_counts: dict = field(default_factory=dict)
    mac_addresses_and_counts: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)

    ips_macs: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts         = None
        self.descriptions_and_counts  = None
        self.source_ips_and_counts    = None
        self.host_names_and_counts    = None
        self.mac_addresses_and_counts = None

        self.milliseconds_and_counts = None

        self.ips_macs = None

@dataclass
class DNSParser(Parser):
    '''
    Parser class for handling DNS log data.

    This class provides functionality for parsing and managing DNS logs,
    including tracking various attributes like times, UDP/TCP indicators,
    source IPs, response codes, and question types/names.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        udp_tcp_indicators_and_counts (dict): Mapping of UDP/TCP indicators to counts.
        send_receive_indicators_and_counts (dict): Mapping of send/receive indicators to counts.
        source_ips_and_counts (dict): Mapping of source IPs to occurrence counts.
        responsecodes_and_counts (dict): Mapping of response codes to counts.
        question_types_and_counts (dict): Mapping of question types to counts.
        question_names_and_counts (dict): Mapping of question names to counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to counts.
        destination_ips_counts (dict): Mapping of destination IPs to counts.
        ips_domains (defaultdict): Mapping of IPs to lists of domains they queried.
        domains_ips (defaultdict): Mapping of domains to lists of IPs that queried them.
        malicious_ips_and_counts (dict): Mapping of malicious IPs to occurrence counts.
        malicious_domains_and_counts (dict): Mapping of malicious domains to occurrence counts.
        ips_domains_counts (dict): Mapping of IPs to counts of unique domains they queried.
        domains_ips_counts (dict): Mapping of domains to counts of unique IPs that queried them.
    '''
    slug: str
    ymd: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    udp_tcp_indicators_and_counts: dict = field(default_factory=dict)
    send_receive_indicators_and_counts: dict = field(default_factory=dict)
    source_ips_and_counts: dict = field(default_factory=dict)
    responsecodes_and_counts: dict = field(default_factory=dict)
    question_types_and_counts: dict = field(default_factory=dict)
    question_names_and_counts: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)
    destination_ips_counts: dict = field(default_factory=dict)

    ips_domains: defaultdict = field(default_factory=lambda: defaultdict(list))
    domains_ips: defaultdict = field(default_factory=lambda: defaultdict(list))

    malicious_ips_and_counts: dict = field(default_factory=dict)
    malicious_domains_and_counts: dict = field(default_factory=dict)

    ips_domains_counts: dict = field(default_factory=dict)
    domains_ips_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts                   = None
        self.udp_tcp_indicators_and_counts      = None
        self.send_receive_indicators_and_counts = None
        self.source_ips_and_counts              = None
        self.responsecodes_and_counts           = None
        self.question_types_and_counts          = None
        self.question_names_and_counts          = None

        self.milliseconds_and_counts = None
        self.destination_ips_counts  = None

        self.ips_domains = None
        self.domains_ips = None

        self.malicious_ips_and_counts = None
        self.malicious_domains_and_counts = None

        self.ips_domains_counts = None
        self.domains_ips_counts = None

@dataclass
class FilterLogParser(Parser):
    '''
    Parser class for handling FilterLog data.

    This class provides functionality for parsing and managing FilterLog logs,
    including tracking various attributes like times, interfaces, reasons,
    actions, and network traffic details.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        tracking_ids_and_counts (dict): Mapping of tracking ids to occurrence counts.
        real_interfaces_and_counts (dict): Mapping of interfaces to occurrence counts.
        reasons_and_counts (dict): Mapping of reasons to occurrence counts.
        actions_and_counts (dict): Mapping of actions to occurrence counts.
        directions_and_counts (dict): Mapping of traffic directions to occurrence counts.
        protocol_names_and_counts (dict): Mapping of protocol names to occurrence counts.
        source_ips_and_counts (dict): Mapping of source IPs to occurrence counts.
        source_ports_and_counts (dict): Mapping of source ports to occurrence counts.
        destination_ips_and_counts (dict): Mapping of destination IPs to occurrence counts.
        destination_ports_and_counts (dict): Mapping of destination ports to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    tracking_ids_and_counts: dict = field(default_factory=dict)
    real_interfaces_and_counts: dict = field(default_factory=dict)
    reasons_and_counts: dict = field(default_factory=dict)
    actions_and_counts: dict = field(default_factory=dict)
    directions_and_counts: dict = field(default_factory=dict)
    protocol_names_and_counts: dict = field(default_factory=dict)
    source_ips_and_counts: dict = field(default_factory=dict)
    source_ports_and_counts: dict = field(default_factory=dict)
    destination_ips_and_counts: dict = field(default_factory=dict)
    destination_ports_and_counts: dict = field(default_factory=dict)

    src_ips_and_dest_ips: defaultdict = field(default_factory=lambda: defaultdict(set))

    milliseconds_and_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts             = None
        self.tracking_ids_and_counts      = None
        self.real_interfaces_and_counts   = None
        self.reasons_and_counts           = None
        self.actions_and_counts           = None
        self.directions_and_counts        = None
        self.protocol_names_and_counts    = None
        self.source_ips_and_counts        = None
        self.source_ports_and_counts      = None
        self.destination_ips_and_counts   = None
        self.destination_ports_and_counts = None

        self.src_ips_and_dest_ips = None

        self.milliseconds_and_counts = None

@dataclass
class RouterParser(Parser):
    '''
    Parser class for handling Router log data.

    This class provides functionality for parsing and managing Router logs.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

@dataclass
class RouterBoardParser(Parser):
    '''
    Parser class for handling RouterBoard log data.

    This class provides functionality for parsing and managing RouterBoard logs.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

@dataclass
class SnortParser(Parser):
    '''
    Parser class for handling Snort intrusion detection system log data.

    This class provides functionality for parsing and managing Snort logs,
    including tracking various attributes like times, alert signatures,
    classifications, and network traffic details.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        gidsids_and_counts (dict): Mapping of generator/signature IDs to occurrence counts.
        descriptions_and_counts (dict): Mapping of alert descriptions to occurrence counts.
        classifications_and_counts (dict): Mapping of alert classifications to occurrence counts.
        priorities_and_counts (dict): Mapping of alert priorities to occurrence counts.
        protocols_and_counts (dict): Mapping of network protocols to occurrence counts.
        source_ips_and_counts (dict): Mapping of source IPs to occurrence counts.
        source_ports_and_counts (dict): Mapping of source ports to occurrence counts.
        destination_ips_and_counts (dict): Mapping of destination IPs to occurrence counts.
        destination_ports_and_counts (dict): Mapping of destination ports to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
        levels_and_counts (dict): Mapping of severity levels to occurrence counts.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    gidsids_and_counts: dict = field(default_factory=dict)
    descriptions_and_counts: dict = field(default_factory=dict)
    classifications_and_counts: dict = field(default_factory=dict)
    priorities_and_counts: dict = field(default_factory=dict)
    protocols_and_counts: dict = field(default_factory=dict)
    source_ips_and_counts: dict = field(default_factory=dict)
    source_ports_and_counts: dict = field(default_factory=dict)
    destination_ips_and_counts: dict = field(default_factory=dict)
    destination_ports_and_counts: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)
    levels_and_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts             = None
        self.gidsids_and_counts           = None
        self.descriptions_and_counts      = None
        self.classifications_and_counts   = None
        self.priorities_and_counts        = None
        self.protocols_and_counts         = None
        self.source_ips_and_counts        = None
        self.source_ports_and_counts      = None
        self.destination_ips_and_counts   = None
        self.destination_ports_and_counts = None

        self.milliseconds_and_counts = None
        self.levels_and_counts = None

@dataclass
class SquidParser(Parser):
    '''
    Parser class for handling Squid proxy log data.

    This class provides functionality for parsing and managing Squid proxy logs,
    including tracking various attributes like times, durations, IPs, request statuses,
    and network traffic details.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        durations_and_counts (dict): Mapping of request durations to occurrence counts.
        source_ips_and_counts (dict): Mapping of source IPs to occurrence counts.
        request_statuses_and_counts (dict): Mapping of request statuses to occurrence counts.
        status_codes_and_counts (dict): Mapping of HTTP status codes to occurrence counts.
        transfers_and_counts (dict): Mapping of data transfer sizes to occurrence counts.
        http_methods_and_counts (dict): Mapping of HTTP methods to occurrence counts.
        urls_and_counts (dict): Mapping of URLs to occurrence counts.
        client_identities_and_counts (dict): Mapping of client identities to occurrence counts.
        peer_codes_and_counts (dict): Mapping of peer status codes to occurrence counts.
        destination_ips_and_counts (dict): Mapping of destination IPs to occurrence counts.
        content_types_and_counts (dict): Mapping of content types to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
        ips_domains (defaultdict): Mapping of IPs to lists of domains accessed.
        domains_ips (defaultdict): Mapping of domains to lists of IPs that accessed them.
        ips_transfers (defaultdict): Mapping of IPs to total data transfer sizes.
        ips_durations (defaultdict): Mapping of IPs to total request durations.
        ips_domains_counts (dict): Mapping of IP-domain pairs to occurrence counts.
        domains_ips_counts (dict): Mapping of domain-IP pairs to occurrence counts.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    durations_and_counts: dict = field(default_factory=dict)
    source_ips_and_counts: dict = field(default_factory=dict)
    request_statuses_and_counts: dict = field(default_factory=dict)
    status_codes_and_counts: dict = field(default_factory=dict)
    transfers_and_counts: dict = field(default_factory=dict)
    http_methods_and_counts: dict = field(default_factory=dict)
    urls_and_counts: dict = field(default_factory=dict)
    client_identities_and_counts: dict = field(default_factory=dict)
    peer_codes_and_counts: dict = field(default_factory=dict)
    destination_ips_and_counts: dict = field(default_factory=dict)
    content_types_and_counts: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)

    ips_domains: defaultdict = field(default_factory=lambda: defaultdict(list))
    domains_ips: defaultdict = field(default_factory=lambda: defaultdict(list))

    ips_transfers: defaultdict = field(default_factory=lambda: defaultdict(int))
    ips_durations: defaultdict = field(default_factory=lambda: defaultdict(int))
    # urls_transfers: defaultdict = field(default_factory=lambda: defaultdict(int))
    # urls_durations: defaultdict = field(default_factory=lambda: defaultdict(int))

    ips_domains_counts: dict = field(default_factory=dict)
    domains_ips_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts             = None
        self.durations_and_counts         = None
        self.source_ips_and_counts        = None
        self.request_statuses_and_counts  = None
        self.status_codes_and_counts      = None
        self.transfers_and_counts         = None
        self.http_methods_and_counts      = None
        self.urls_and_counts              = None
        self.client_identities_and_counts = None
        self.peer_codes_and_counts        = None
        self.destination_ips_and_counts   = None
        self.content_types_and_counts     = None

        self.milliseconds_and_counts = None

        self.ips_domains   = None
        self.domains_ips   = None
        self.ips_transfers = None
        self.ips_durations = None
        # self.urls_transfers = None
        # self.urls_durations = None

        self.ips_domains_counts = None
        self.domains_ips_counts = None

@dataclass
class SwitchParser(Parser):
    '''
    Parser class for handling Switch log data.

    This class provides functionality for parsing and managing Switch logs.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

@dataclass
class UserAuditParser(Parser):
    '''
    Parser class for handling User Audit log data.

    This class provides functionality for parsing and managing user audit logs.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

@dataclass
class UserNoticeParser(Parser):
    '''
    Parser class for handling User Notice log data.

    This class provides functionality for parsing and managing user notice logs,
    including tracking various attributes like times, servers, users, and connection details.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        servers_and_counts (dict): Mapping of servers to occurrence counts.
        users_and_counts (dict): Mapping of users to occurrence counts.
        destinationips_and_counts (dict): Mapping of destination IPs to occurrence counts.
        ports_and_counts (dict): Mapping of ports to occurrence counts.
        statuses_and_counts (dict): Mapping of connection statuses to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    servers_and_counts: dict = field(default_factory=dict)
    users_and_counts: dict = field(default_factory=dict)
    destinationips_and_counts: dict = field(default_factory=dict)
    ports_and_counts: dict = field(default_factory=dict)
    statuses_and_counts: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts          = None
        self.servers_and_counts        = None
        self.users_and_counts          = None
        self.destinationips_and_counts = None
        self.ports_and_counts          = None
        self.statuses_and_counts       = None

        self.milliseconds_and_counts = None

@dataclass
class UserWarningParser(Parser):
    '''
    Parser class for handling User Warning log data.

    This class provides functionality for parsing and managing user warning logs,
    particularly related to gateway packet loss monitoring.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        gateways_dict (dict): Mapping of gateway identifiers to their names.
        rows (list): Collection of parsed entries.
        gateway_rows (list): Collection of gateway packet loss data rows.
    '''
    slug: str
    ymd: str
    object_name: str
    gateways_dict: dict
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    # for packetlosstable
    gateway_rows: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.gateways_dict = None

        ## for packetlosstable
        self.gateway_rows = None

@dataclass
class VMwareParser(Parser):
    '''
    Parser class for handling VMware log data.

    This class provides functionality for parsing and managing VMware logs.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        rows (list): Collection of parsed entries.
    '''
    slug: str
    ymd: str
    object_name: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

@dataclass
class WindowsServerParser(Parser):
    '''
    Parser class for handling Windows Server log data.

    This class provides functionality for parsing and managing Windows Server logs,
    including tracking various attributes like times, event IDs, categories, and account details.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        object_name (str): Name of the object.
        category (str, optional): Specific category to parse.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        eventids_and_counts (dict): Mapping of event IDs to occurrence counts.
        categories_and_counts (dict): Mapping of log categories to occurrence counts.
        potentialcriticalities_and_counts (dict): Mapping of criticality levels to occurrence counts.
        accountnames_and_counts (dict): Mapping of account names to occurrence counts.
        accountdomains_and_counts (dict): Mapping of account domains to occurrence counts.
        sourceworkstations_and_counts (dict): Mapping of source workstations to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
    '''
    slug: str
    ymd: str
    object_name: str
    category: str = None
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    eventids_and_counts: dict = field(default_factory=dict)
    categories_and_counts: dict = field(default_factory=dict)
    potentialcriticalities_and_counts: dict = field(default_factory=dict)
    accountnames_and_counts: dict = field(default_factory=dict)
    accountdomains_and_counts: dict = field(default_factory=dict)
    sourceworkstations_and_counts: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts                  = None
        self.eventids_and_counts               = None
        self.categories_and_counts             = None
        self.potentialcriticalities_and_counts = None
        self.accountnames_and_counts           = None
        self.accountdomains_and_counts         = None
        self.sourceworkstations_and_counts     = None

        self.milliseconds_and_counts = None

@dataclass
class VPNServerParser(Parser):
    '''
    Parser class for handling VPN Server log data.

    This class provides functionality for parsing and managing VPN Server logs,
    including tracking various attributes like times, domains, usernames, ports,
    source ips, and destination ips.

    Attributes:
        slug (str): Identifier for the parser type.
        ymd (str): Date in YYYY-MM-DD format.
        rows (list): Collection of parsed entries.
        times_and_counts (dict): Mapping of hour keys to occurrence counts.
        domains_and_counts (dict): Mapping of usernames to occurrence counts.
        usernames_and_counts (dict): Mapping of usernames to occurrence counts.
        ports_and_counts (dict): Mapping of ports to occurrence counts.
        source_ips_and_counts (dict): Mapping of source IPs to occurrence counts.
        milliseconds_and_counts (dict): Mapping of millisecond values to occurrence counts.
    '''
    slug: str
    ymd: str
    rows: list = field(default_factory=list)

    # _instances: list = field(default_factory=list)

    times_and_counts: dict = field(default_factory=lambda: {hour_key: 0 for hour_key in HOUR_KEYS})
    domains_and_counts: dict = field(default_factory=dict)
    usernames_and_counts: dict = field(default_factory=dict)
    ports_and_counts: dict = field(default_factory=dict)
    source_ips_and_counts: dict = field(default_factory=dict)

    username_aggregates: dict = field(default_factory=dict)

    milliseconds_and_counts: dict = field(default_factory=dict)

    def __post_init__(self):
        # __class__._instances.append(self)
        pass

    # @classmethod
    # def get_instances(cls):
    #     return cls._instances

    def truncate_all(self):
        self.truncate_rows()

        self.times_and_counts           = None
        self.domains_and_counts         = None
        self.usernames_and_counts       = None
        self.ports_and_counts           = None
        self.source_ips_and_counts      = None

        self.username_aggregates = None

        self.milliseconds_and_counts = None
